import axios from 'axios'

const BASE_URL = 'http://localhost:5000'

async function testAuthRoute() {
  try {
    console.log('Testing auth route...')
    
    // This should return a 401 or 400 error, but not a 404
    const response = await axios.post(`${BASE_URL}/api/auth/admin/login`, {
      username: 'test',
      password: 'test'
    })
    console.log('Unexpected success:', response.data)
    
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 400) {
      console.log('✅ Auth route working (returned expected error)')
      console.log('Status:', error.response.status)
      console.log('Response:', error.response.data)
    } else if (error.response?.status === 404) {
      console.log('❌ Auth route not found (404)')
    } else {
      console.error('❌ Auth route failed:', error.response?.data || error.message)
    }
  }
}

testAuthRoute()
