import express from "express"
import { getCalendarTasks, getCalendarStats } from "../controllers/calendarController.js"
import { verifyToken, employeeOnly } from "../middleware/auth.js"

console.log('📅 Calendar routes file loaded')

const router = express.Router()

// Test route (no auth required)
router.get("/test", (req, res) => {
  console.log('📅 Calendar test route hit!')
  res.json({ message: "Calendar routes are working!" })
})

// Apply authentication middleware to protected routes
router.use(verifyToken)
router.use(employeeOnly)

// Calendar routes
router.get("/tasks", getCalendarTasks)
router.get("/stats", getCalendarStats)

export default router
