"use client"

import { useState, useEffect } from "react"
import { Button } from "./ui/Button"
import { Input } from "./ui/Input"
import { Select } from "./ui/Select"
import { Card } from "./ui/Card"
import { personalTaskService } from "../services/personalTaskService"
import { Plus, X, Save, ArrowLeft } from "lucide-react"

const STATUS_OPTIONS = [
  { value: "to do", label: "To Do" },
  { value: "In Progress", label: "In Progress" },
  { value: "On Hold", label: "On Hold" },
  { value: "Completed", label: "Completed" },
  { value: "Cancelled", label: "Cancelled" },
]

const PersonalTaskForm = ({ task, onSave, onCancel, isEditing = false }) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    status: "to do",
    estimatedHours: 0,
    estimatedMinutes: 0,
    subtasks: [],
    dueDate: "",
    startDate: "",
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  useEffect(() => {
    if (task && isEditing) {
      setFormData({
        title: task.title || "",
        description: task.description || "",
        status: task.status || "to do",
        estimatedHours: task.estimatedHours || 0,
        estimatedMinutes: task.estimatedMinutes || 0,
        subtasks: task.subtasks || [],
        dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : "",
        startDate: task.startDate ? new Date(task.startDate).toISOString().split('T')[0] : "",
      })
    }
  }, [task, isEditing])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleNumberChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: Number.parseInt(value) || 0,
    }))
  }

  const addSubtask = () => {
    setFormData((prev) => ({
      ...prev,
      subtasks: [
        ...prev.subtasks,
        {
          title: "",
          description: "",
          status: "to do",
          estimatedHours: 0,
          estimatedMinutes: 0,
        },
      ],
    }))
  }

  const removeSubtask = (index) => {
    setFormData((prev) => ({
      ...prev,
      subtasks: prev.subtasks.filter((_, i) => i !== index),
    }))
  }

  const updateSubtask = (index, field, value) => {
    setFormData((prev) => ({
      ...prev,
      subtasks: prev.subtasks.map((subtask, i) => (i === index ? { ...subtask, [field]: value } : subtask)),
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      if (!formData.title.trim()) {
        throw new Error("Task title is required")
      }

      let result
      if (isEditing && task) {
        result = await personalTaskService.updateTask(task._id, formData)
      } else {
        result = await personalTaskService.createTask(formData)
      }

      onSave(result.task)
    } catch (err) {
      setError(err.response?.data?.message || err.message || "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" onClick={onCancel} className="flex items-center gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">{isEditing ? "Edit Personal Task" : "Create Personal Task"}</h1>
      </div>

      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">{error}</div>}

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Task Details</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-2">Task Title *</label>
              <Input
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter task title"
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-2">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter task description"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Status</label>
              <Select name="status" value={formData.status} onChange={handleInputChange} options={STATUS_OPTIONS} />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Estimated Time</label>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    type="number"
                    name="estimatedHours"
                    value={formData.estimatedHours}
                    onChange={handleNumberChange}
                    placeholder="Hours"
                    min="0"
                  />
                </div>
                <div className="flex-1">
                  <Input
                    type="number"
                    name="estimatedMinutes"
                    value={formData.estimatedMinutes}
                    onChange={handleNumberChange}
                    placeholder="Minutes"
                    min="0"
                    max="59"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Start Date</label>
                <Input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Due Date</label>
                <Input
                  type="date"
                  name="dueDate"
                  value={formData.dueDate}
                  onChange={handleInputChange}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Subtasks</h2>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addSubtask}
              className="flex items-center gap-2 bg-transparent"
            >
              <Plus className="w-4 h-4" />
              Add Subtask
            </Button>
          </div>

          {formData.subtasks.length === 0 ? (
            <p className="text-gray-500 text-center py-8">No subtasks added. Click "Add Subtask" to create one.</p>
          ) : (
            <div className="space-y-4">
              {formData.subtasks.map((subtask, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium">Subtask {index + 1}</h3>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSubtask(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <Input
                        value={subtask.title}
                        onChange={(e) => updateSubtask(index, "title", e.target.value)}
                        placeholder="Subtask title"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <textarea
                        value={subtask.description}
                        onChange={(e) => updateSubtask(index, "description", e.target.value)}
                        placeholder="Subtask description"
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div>
                      <Select
                        value={subtask.status}
                        onChange={(e) => updateSubtask(index, "status", e.target.value)}
                        options={STATUS_OPTIONS}
                      />
                    </div>

                    <div>
                      <div className="flex gap-2">
                        <Input
                          type="number"
                          value={subtask.estimatedHours}
                          onChange={(e) => updateSubtask(index, "estimatedHours", Number.parseInt(e.target.value) || 0)}
                          placeholder="Hours"
                          min="0"
                        />
                        <Input
                          type="number"
                          value={subtask.estimatedMinutes}
                          onChange={(e) =>
                            updateSubtask(index, "estimatedMinutes", Number.parseInt(e.target.value) || 0)
                          }
                          placeholder="Minutes"
                          min="0"
                          max="59"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading} className="flex items-center gap-2">
            <Save className="w-4 h-4" />
            {loading ? "Saving..." : isEditing ? "Update Task" : "Create Task"}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default PersonalTaskForm
