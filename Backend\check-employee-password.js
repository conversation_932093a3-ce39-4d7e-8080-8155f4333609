import mongoose from 'mongoose';
import Employee from './models/Employee.js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

mongoose.connect(process.env.MONGO_URI).then(async () => {
  console.log('=== CHECKING EMPLOYEE PASSWORD ===');
  
  const employee = await Employee.findOne({ username: 'maryse' });
  
  if (!employee) {
    console.log('Employee "maryse" not found');
    process.exit(1);
  }
  
  console.log(`Found employee: ${employee.name} (${employee.username})`);
  console.log(`Email verified: ${employee.isEmailVerified}`);
  console.log(`Active: ${employee.isActive}`);
  
  // Test common passwords
  const testPasswords = ['password123', 'password', '123456', 'maryse123', 'admin123', 'employee123', 'test123', 'default123', 'user123', 'maryse', 'Password123', 'PASSWORD123'];
  
  for (const password of testPasswords) {
    const isMatch = await bcrypt.compare(password, employee.password);
    console.log(`Password "${password}": ${isMatch ? '✅ MATCH' : '❌ No match'}`);
    
    if (isMatch) {
      console.log(`\n🎉 Correct password found: "${password}"`);
      break;
    }
  }
  
  process.exit(0);
}).catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
