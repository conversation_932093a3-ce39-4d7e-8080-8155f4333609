/* Custom styles for react-big-calendar */
.rbc-calendar-custom {
  font-family: inherit;
  background: transparent;
}

.rbc-calendar-custom .rbc-header {
  background: rgb(248 250 252);
  border-bottom: 1px solid rgb(226 232 240);
  color: rgb(51 65 85);
  font-weight: 600;
  padding: 12px 8px;
  text-align: center;
}

.dark .rbc-calendar-custom .rbc-header {
  background: rgb(30 41 59);
  border-bottom: 1px solid rgb(51 65 85);
  color: rgb(226 232 240);
}

.rbc-calendar-custom .rbc-month-view {
  border: 1px solid rgb(226 232 240);
  border-radius: 8px;
  overflow: hidden;
}

.dark .rbc-calendar-custom .rbc-month-view {
  border: 1px solid rgb(51 65 85);
}

.rbc-calendar-custom .rbc-date-cell {
  padding: 8px;
  text-align: right;
  border-right: 1px solid rgb(226 232 240);
  border-bottom: 1px solid rgb(226 232 240);
  background: white;
  min-height: 100px;
}

.dark .rbc-calendar-custom .rbc-date-cell {
  border-right: 1px solid rgb(51 65 85);
  border-bottom: 1px solid rgb(51 65 85);
  background: rgb(15 23 42);
  color: rgb(226 232 240);
}

.rbc-calendar-custom .rbc-date-cell.rbc-off-range {
  background: rgb(248 250 252);
  color: rgb(148 163 184);
}

.dark .rbc-calendar-custom .rbc-date-cell.rbc-off-range {
  background: rgb(30 41 59);
  color: rgb(100 116 139);
}

.rbc-calendar-custom .rbc-date-cell.rbc-today {
  background: rgb(239 246 255);
  color: rgb(37 99 235);
  font-weight: 600;
}

.dark .rbc-calendar-custom .rbc-date-cell.rbc-today {
  background: rgb(30 58 138);
  color: rgb(147 197 253);
}

.rbc-calendar-custom .rbc-event {
  border: none;
  border-radius: 4px;
  padding: 2px 6px;
  margin: 1px 0;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rbc-calendar-custom .rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.rbc-calendar-custom .rbc-event-label {
  font-size: 10px;
  font-weight: 400;
  opacity: 0.8;
}

.rbc-calendar-custom .rbc-show-more {
  background: rgb(99 102 241);
  color: white;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  margin: 1px 0;
}

.rbc-calendar-custom .rbc-show-more:hover {
  background: rgb(79 70 229);
}

.dark .rbc-calendar-custom .rbc-show-more {
  background: rgb(129 140 248);
  color: rgb(15 23 42);
}

.dark .rbc-calendar-custom .rbc-show-more:hover {
  background: rgb(165 180 252);
}

/* Week and Day view styles */
.rbc-calendar-custom .rbc-time-view {
  border: 1px solid rgb(226 232 240);
  border-radius: 8px;
  overflow: hidden;
}

.dark .rbc-calendar-custom .rbc-time-view {
  border: 1px solid rgb(51 65 85);
}

.rbc-calendar-custom .rbc-time-header {
  background: rgb(248 250 252);
  border-bottom: 1px solid rgb(226 232 240);
}

.dark .rbc-calendar-custom .rbc-time-header {
  background: rgb(30 41 59);
  border-bottom: 1px solid rgb(51 65 85);
}

.rbc-calendar-custom .rbc-time-content {
  background: white;
}

.dark .rbc-calendar-custom .rbc-time-content {
  background: rgb(15 23 42);
}

.rbc-calendar-custom .rbc-time-slot {
  border-top: 1px solid rgb(241 245 249);
}

.dark .rbc-calendar-custom .rbc-time-slot {
  border-top: 1px solid rgb(51 65 85);
}

.rbc-calendar-custom .rbc-timeslot-group {
  border-bottom: 1px solid rgb(226 232 240);
}

.dark .rbc-calendar-custom .rbc-timeslot-group {
  border-bottom: 1px solid rgb(51 65 85);
}

.rbc-calendar-custom .rbc-day-slot .rbc-time-slot {
  color: rgb(100 116 139);
}

.dark .rbc-calendar-custom .rbc-day-slot .rbc-time-slot {
  color: rgb(148 163 184);
}

/* Popup styles */
.rbc-calendar-custom .rbc-overlay {
  background: white;
  border: 1px solid rgb(226 232 240);
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 12px;
}

.dark .rbc-calendar-custom .rbc-overlay {
  background: rgb(30 41 59);
  border: 1px solid rgb(51 65 85);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.rbc-calendar-custom .rbc-overlay-header {
  font-weight: 600;
  color: rgb(51 65 85);
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgb(226 232 240);
}

.dark .rbc-calendar-custom .rbc-overlay-header {
  color: rgb(226 232 240);
  border-bottom: 1px solid rgb(51 65 85);
}

/* Toolbar styles */
.rbc-calendar-custom .rbc-toolbar {
  display: none; /* We're using our custom toolbar */
}

/* Current time indicator */
.rbc-calendar-custom .rbc-current-time-indicator {
  background: rgb(239 68 68);
  height: 2px;
  z-index: 10;
}

.rbc-calendar-custom .rbc-current-time-indicator::before {
  content: '';
  position: absolute;
  left: -6px;
  top: -3px;
  width: 8px;
  height: 8px;
  background: rgb(239 68 68);
  border-radius: 50%;
}

/* Selection styles */
.rbc-calendar-custom .rbc-selected {
  background: rgb(99 102 241) !important;
  color: white !important;
}

.rbc-calendar-custom .rbc-selected:hover {
  background: rgb(79 70 229) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-calendar-custom .rbc-date-cell {
    min-height: 80px;
    padding: 4px;
  }
  
  .rbc-calendar-custom .rbc-event {
    font-size: 10px;
    padding: 1px 4px;
  }
  
  .rbc-calendar-custom .rbc-show-more {
    font-size: 9px;
    padding: 1px 4px;
  }
}
