import axios from 'axios'

const BASE_URL = 'http://localhost:5000'

async function testRoutes() {
  try {
    console.log('Testing available routes...\n')

    // Test root endpoint
    console.log('1. Testing root endpoint...')
    const rootResponse = await axios.get(`${BASE_URL}/`)
    console.log('✅ Root endpoint working:', rootResponse.data)

    // Test a known working endpoint
    console.log('\n2. Testing known working endpoint (/api/auth/admin/login)...')
    try {
      const authResponse = await axios.post(`${BASE_URL}/api/auth/admin/login`, {
        username: 'test',
        password: 'test'
      })
      console.log('Auth endpoint response:', authResponse.status)
    } catch (authError) {
      if (authError.response?.status === 401) {
        console.log('✅ Auth endpoint working (returned 401 as expected)')
      } else {
        console.log('Auth endpoint error:', authError.response?.status || authError.message)
      }
    }

    // Test calendar endpoint without auth
    console.log('\n3. Testing calendar endpoint without auth...')
    try {
      const calendarResponse = await axios.get(`${BASE_URL}/api/calendar/tasks`)
      console.log('Calendar endpoint response:', calendarResponse.status)
    } catch (calendarError) {
      if (calendarError.response?.status === 401) {
        console.log('✅ Calendar endpoint exists (returned 401 - needs auth)')
      } else if (calendarError.response?.status === 404) {
        console.log('❌ Calendar endpoint not found (404)')
      } else {
        console.log('Calendar endpoint error:', calendarError.response?.status || calendarError.message)
      }
    }

    // List all registered routes (if possible)
    console.log('\n4. Testing various endpoints to see what exists...')
    const testEndpoints = [
      '/api/personal-task/my-tasks',
      '/api/project/assigned',
      '/api/calendar/tasks',
      '/api/calendar/stats'
    ]

    for (const endpoint of testEndpoints) {
      try {
        await axios.get(`${BASE_URL}${endpoint}`)
        console.log(`✅ ${endpoint} - exists`)
      } catch (error) {
        if (error.response?.status === 401) {
          console.log(`✅ ${endpoint} - exists (needs auth)`)
        } else if (error.response?.status === 404) {
          console.log(`❌ ${endpoint} - not found`)
        } else {
          console.log(`? ${endpoint} - ${error.response?.status || 'unknown error'}`)
        }
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testRoutes()
