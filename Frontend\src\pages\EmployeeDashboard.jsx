"use client"

import { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"
import { projectService, projectTaskService } from "../services/projectService"
import { Navigation } from "../components/ui/Navigation"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "../components/ui/Card"
import { Button } from "../components/ui/Button"
import { Badge } from "../components/ui/Badge"
import { Avatar } from "../components/ui/Avatar"
import ProjectList from "../components/ProjectList"
import TaskList from "../components/TaskList"
import TaskForm from "../components/TaskForm"
import TaskDetail from "../components/TaskDetail"
import PersonalTaskList from "../components/PersonalTaskList"
import PersonalTaskForm from "../components/PersonalTaskForm"
import PersonalTaskDetail from "../components/PersonalTaskDetail"
import Calendar from "../components/Calendar"
import { personalTaskService } from "../services/personalTaskService"

export default function EmployeeDashboard() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState("overview")
  const [projects, setProjects] = useState([])
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalProjects: 0,
    totalTasks: 0,
    completedTasks: 0,
    pendingTasks: 0,
  })

  // Task management state
  const [selectedProject, setSelectedProject] = useState(null)
  const [selectedTask, setSelectedTask] = useState(null)
  const [showTaskForm, setShowTaskForm] = useState(false)
  const [editingTask, setEditingTask] = useState(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // Personal task management state
  const [personalTasks, setPersonalTasks] = useState([])
  const [selectedPersonalTask, setSelectedPersonalTask] = useState(null)
  const [showPersonalTaskForm, setShowPersonalTaskForm] = useState(false)
  const [editingPersonalTask, setEditingPersonalTask] = useState(null)
  const [personalTaskRefreshTrigger, setPersonalTaskRefreshTrigger] = useState(0)

  // Fetch assigned projects and tasks
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)

        // Fetch assigned projects
        const projectsResponse = await projectService.getAssignedProjects()
        setProjects(projectsResponse.data || [])

        // Fetch my tasks
        const tasksResponse = await projectTaskService.getMyTasks({ limit: 10 })
        setTasks(tasksResponse.data || [])

        // Fetch personal tasks
        const personalTasksResponse = await personalTaskService.getMyTasks({ limit: 10 })
        setPersonalTasks(personalTasksResponse.tasks || [])

        // Calculate stats
        const totalTasks = tasksResponse.data?.length || 0
        const completedTasks = tasksResponse.data?.filter((task) => task.status === "completed").length || 0
        const pendingTasks = tasksResponse.data?.filter((task) => task.status === "to do").length || 0

        // Update stats to include personal tasks
        const personalTaskStats = personalTasksResponse.tasks || []
        const personalCompletedTasks = personalTaskStats.filter((task) => task.status === "Completed").length
        const personalPendingTasks = personalTaskStats.filter((task) => task.status === "to do").length

        setStats({
          totalProjects: projectsResponse.data?.length || 0,
          totalTasks: (tasksResponse.data?.length || 0) + personalTaskStats.length,
          completedTasks: completedTasks + personalCompletedTasks,
          pendingTasks: pendingTasks + personalPendingTasks,
        })
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
      } finally {
        setLoading(false)
      }
    }

    if (user?.role === "employee") {
      fetchData()
    }
  }, [user])

  // Task management functions
  const handleProjectSelect = (project) => {
    setSelectedProject(project)
    setActiveTab("project-tasks")
    setSelectedTask(null)
    setShowTaskForm(false)
    setEditingTask(null)
  }

  const handleCreateTask = () => {
    setShowTaskForm(true)
    setEditingTask(null)
    setSelectedTask(null)
  }

  const handleEditTask = (task) => {
    setEditingTask(task)
    setShowTaskForm(true)
    setSelectedTask(null)
  }

  const handleTaskSelect = (task) => {
    setSelectedTask(task)
    setShowTaskForm(false)
    setEditingTask(null)
  }

  const handleTaskFormSubmit = async (taskData) => {
    try {
      if (editingTask) {
        await projectTaskService.updateTask(editingTask._id, taskData)
      } else {
        await projectTaskService.createTask(taskData)
      }
      setShowTaskForm(false)
      setEditingTask(null)
      setRefreshTrigger((prev) => prev + 1)
      // Refresh dashboard data
      const tasksResponse = await projectTaskService.getMyTasks({ limit: 10 })
      setTasks(tasksResponse.data || [])
    } catch (error) {
      console.error("Error saving task:", error)
      alert("Failed to save task. Please try again.")
    }
  }

  const handleTaskFormCancel = () => {
    setShowTaskForm(false)
    setEditingTask(null)
  }

  const handleTaskUpdate = (updatedTask) => {
    setSelectedTask(updatedTask)
    setRefreshTrigger((prev) => prev + 1)
  }

  const handleTaskDelete = async (taskId) => {
    if (!window.confirm("Are you sure you want to delete this task?")) return

    try {
      await projectTaskService.deleteTask(taskId)
      setSelectedTask(null)
      setRefreshTrigger((prev) => prev + 1)
      // Refresh dashboard data
      const tasksResponse = await projectTaskService.getMyTasks({ limit: 10 })
      setTasks(tasksResponse.data || [])
    } catch (error) {
      console.error("Error deleting task:", error)
      alert("Failed to delete task. Please try again.")
    }
  }

  const handleBackToProjects = () => {
    setSelectedProject(null)
    setSelectedTask(null)
    setShowTaskForm(false)
    setEditingTask(null)
    setActiveTab("overview")
  }

  const handleBackToTasks = () => {
    setSelectedTask(null)
    setShowTaskForm(false)
    setEditingTask(null)
  }

  // Personal task management functions
  const handleCreatePersonalTask = () => {
    setShowPersonalTaskForm(true)
    setEditingPersonalTask(null)
    setSelectedPersonalTask(null)
    setActiveTab("personal-tasks")
  }

  const handleEditPersonalTask = (task) => {
    setEditingPersonalTask(task)
    setShowPersonalTaskForm(true)
    setSelectedPersonalTask(null)
  }

  const handlePersonalTaskSelect = (task) => {
    setSelectedPersonalTask(task)
    setShowPersonalTaskForm(false)
    setEditingPersonalTask(null)
  }

  const handlePersonalTaskFormSubmit = async (taskData) => {
    try {
      if (editingPersonalTask) {
        await personalTaskService.updateTask(editingPersonalTask._id, taskData)
      } else {
        await personalTaskService.createTask(taskData)
      }
      setShowPersonalTaskForm(false)
      setEditingPersonalTask(null)
      setPersonalTaskRefreshTrigger((prev) => prev + 1)
      // Refresh dashboard data
      const personalTasksResponse = await personalTaskService.getMyTasks({ limit: 10 })
      setPersonalTasks(personalTasksResponse.tasks || [])
    } catch (error) {
      console.error("Error saving personal task:", error)
      alert("Failed to save personal task. Please try again.")
    }
  }

  const handlePersonalTaskFormCancel = () => {
    setShowPersonalTaskForm(false)
    setEditingPersonalTask(null)
  }

  const handlePersonalTaskUpdate = (updatedTask) => {
    setSelectedPersonalTask(updatedTask)
    setPersonalTaskRefreshTrigger((prev) => prev + 1)
  }

  const handlePersonalTaskDelete = async (taskId) => {
    if (!window.confirm("Are you sure you want to delete this personal task?")) return

    try {
      await personalTaskService.deleteTask(taskId)
      setSelectedPersonalTask(null)
      setPersonalTaskRefreshTrigger((prev) => prev + 1)
      // Refresh dashboard data
      const personalTasksResponse = await personalTaskService.getMyTasks({ limit: 10 })
      setPersonalTasks(personalTasksResponse.tasks || [])
    } catch (error) {
      console.error("Error deleting personal task:", error)
      alert("Failed to delete personal task. Please try again.")
    }
  }

  const handleBackToPersonalTasks = () => {
    setSelectedPersonalTask(null)
    setShowPersonalTaskForm(false)
    setEditingPersonalTask(null)
  }

  const navigationActions = [
    {
      label: "Overview",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"
          />
        </svg>
      ),
      onClick: () => {
        setActiveTab("overview")
        setSelectedProject(null)
        setSelectedTask(null)
        setShowTaskForm(false)
        setEditingTask(null)
        setSelectedPersonalTask(null)
        setShowPersonalTaskForm(false)
        setEditingPersonalTask(null)
      },
      variant: activeTab === "overview" ? "primary" : "ghost",
    },
    {
      label: "My Projects",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
      ),
      onClick: () => {
        setActiveTab("projects")
        setSelectedProject(null)
        setSelectedTask(null)
        setShowTaskForm(false)
        setEditingTask(null)
        setSelectedPersonalTask(null)
        setShowPersonalTaskForm(false)
        setEditingPersonalTask(null)
      },
      variant: activeTab === "projects" ? "primary" : "ghost",
    },
    {
      label: "Profile",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
      ),
      onClick: () => {
        setActiveTab("profile")
        setSelectedProject(null)
        setSelectedTask(null)
        setShowTaskForm(false)
        setEditingTask(null)
        setSelectedPersonalTask(null)
        setShowPersonalTaskForm(false)
        setEditingPersonalTask(null)
      },
      variant: activeTab === "profile" ? "primary" : "ghost",
    },
    {
      label: "Personal Tasks",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7H9m3 0h3M9 14h.01M12 14h.01M15 14h.01M12 11h.01M15 11h.01M12 8h.01M15 8h.01"
          />
        </svg>
      ),
      onClick: () => {
        setActiveTab("personal-tasks")
        setSelectedProject(null)
        setSelectedTask(null)
        setShowTaskForm(false)
        setEditingTask(null)
        setSelectedPersonalTask(null)
        setShowPersonalTaskForm(false)
        setEditingPersonalTask(null)
      },
      variant: activeTab === "personal-tasks" ? "primary" : "ghost",
    },
    {
      label: "Calendar",
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      ),
      onClick: () => {
        setActiveTab("calendar")
        setSelectedProject(null)
        setSelectedTask(null)
        setShowTaskForm(false)
        setEditingTask(null)
        setSelectedPersonalTask(null)
        setShowPersonalTaskForm(false)
        setEditingPersonalTask(null)
      },
      variant: activeTab === "calendar" ? "primary" : "ghost",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "success"
      case "in progress":
        return "warning"
      case "to do":
        return "default"
      case "on hold":
        return "secondary"
      case "cancelled":
        return "destructive"
      default:
        return "default"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "urgent":
        return "danger"
      case "high":
        return "warning"
      case "medium":
        return "info"
      case "low":
        return "default"
      default:
        return "default"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-indigo-50 to-blue-50 dark:from-slate-950 dark:via-slate-900 dark:to-indigo-950">
      <Navigation title="Employee Dashboard" subtitle={`Welcome back, ${user?.name}`} actions={navigationActions} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Welcome Section */}
        <div className="text-center py-6">
          <h1 className="text-3xl font-bold text-gradient mb-3">My Workspace</h1>
          <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Track your tasks, manage your time, and stay productive
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card variant="floating" className="group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-1">Total Projects</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                    {loading ? "..." : stats.totalProjects}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900/30 dark:to-indigo-800/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                  <svg
                    className="w-6 h-6 text-indigo-600 dark:text-indigo-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                    />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="floating" className="group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-1">Total Tasks</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors">
                    {loading ? "..." : stats.totalTasks}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                  <svg
                    className="w-6 h-6 text-emerald-600 dark:text-emerald-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="floating" className="group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-1">Completed</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors">
                    {loading ? "..." : stats.completedTasks}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/30 dark:to-amber-800/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                  <svg
                    className="w-6 h-6 text-amber-600 dark:text-amber-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="floating" className="group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-1">Pending</p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                    {loading ? "..." : stats.pendingTasks}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                  <svg
                    className="w-6 h-6 text-purple-600 dark:text-purple-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Area */}
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Recent Tasks */}
            <div className="lg:col-span-2">
              <Card variant="modern" className="overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-slate-50 to-indigo-50 dark:from-slate-800 dark:to-indigo-900/20 border-b border-slate-200 dark:border-slate-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle gradient className="text-2xl">
                        Recent Tasks
                      </CardTitle>
                      <CardDescription>Your latest task assignments and updates</CardDescription>
                    </div>
                    <Button
                      variant="glass"
                      size="sm"
                      className="backdrop-blur-sm"
                      onClick={() => setActiveTab("projects")}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                      View Projects
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {loading ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                      </div>
                    ) : tasks.length > 0 ? (
                      tasks.slice(0, 5).map((task) => (
                        <div
                          key={task._id}
                          className="group flex items-center justify-between p-4 bg-slate-50/50 dark:bg-slate-800/50 rounded-xl border border-slate-200/50 dark:border-slate-700/50 hover:bg-white dark:hover:bg-slate-800 hover:shadow-md transition-all duration-300 hover:scale-[1.02] cursor-pointer"
                          onClick={() => handleTaskSelect(task)}
                        >
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform shadow-lg">
                              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                                />
                              </svg>
                            </div>
                            <div>
                              <h4 className="font-semibold text-slate-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                                {task.name}
                              </h4>
                              <p className="text-sm text-slate-600 dark:text-slate-400">
                                Project: {task.project?.name} • Created: {new Date(task.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge variant={getStatusColor(task.status)} size="sm">
                              {task.status}
                            </Badge>
                            {task.subtasks?.length > 0 && (
                              <Badge variant="outline" size="sm">
                                {task.completedSubtasksCount}/{task.subtasks.length} subtasks
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500 dark:text-gray-400">No tasks found. Create your first task!</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions & Profile */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <Card variant="glass">
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Frequently used features</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button variant="primary" className="w-full justify-start" onClick={handleCreatePersonalTask}>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                      Create Personal Task
                    </Button>
                    <Button variant="secondary" className="w-full justify-start">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      Log Time
                    </Button>
                    <Button variant="secondary" className="w-full justify-start">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        />
                      </svg>
                      View Reports
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Profile Summary */}
              <Card variant="glass">
                <CardHeader>
                  <CardTitle>Profile</CardTitle>
                  <CardDescription>Your account information</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4 mb-4">
                    <Avatar src={user?.avatar} alt={user?.name} size="lg" status="online" />
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">{user?.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{user?.email}</p>
                      <Badge variant="success" size="sm" className="mt-1">
                        {user?.role}
                      </Badge>
                    </div> 
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Department:</span>
                      <span className="font-medium text-gray-900 dark:text-white">{user?.department?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Employee ID:</span>
                      <span className="font-medium text-gray-900 dark:text-white">{user?.employeeId || "EMP001"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Join Date:</span>
                      <span className="font-medium text-gray-900 dark:text-white">{user?.joinDate || "Jan 2024"}</span>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full mt-4 bg-transparent">
                    Edit Profile
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Projects View */}
        {activeTab === "projects" && <ProjectList onProjectSelect={handleProjectSelect} />}

        {/* Project Tasks View */}
        {activeTab === "project-tasks" && selectedProject && !showTaskForm && !selectedTask && (
          <TaskList
            project={selectedProject}
            onTaskSelect={handleTaskSelect}
            onCreateTask={handleCreateTask}
            onEditTask={handleEditTask}
            onDeleteTask={handleTaskDelete}
            refreshTrigger={refreshTrigger}
          />
        )}

        {/* Task Form View */}
        {showTaskForm && selectedProject && (
          <TaskForm
            task={editingTask}
            project={selectedProject}
            onSubmit={handleTaskFormSubmit}
            onCancel={handleTaskFormCancel}
          />
        )}

        {/* Task Detail View */}
        {selectedTask && !showTaskForm && (
          <TaskDetail
            task={selectedTask}
            onEdit={handleEditTask}
            onDelete={handleTaskDelete}
            onBack={selectedProject ? handleBackToTasks : () => setActiveTab("overview")}
            onTaskUpdate={handleTaskUpdate}
          />
        )}

        {/* Personal Tasks View */}
        {activeTab === "personal-tasks" && !showPersonalTaskForm && !selectedPersonalTask && (
          <PersonalTaskList
            onViewTask={handlePersonalTaskSelect}
            onCreateTask={handleCreatePersonalTask}
            onEditTask={handleEditPersonalTask}
            onDeleteTask={handlePersonalTaskDelete}
            refreshTrigger={personalTaskRefreshTrigger}
          />
        )}

        {/* Personal Task Form View */}
        {showPersonalTaskForm && activeTab === "personal-tasks" && (
          <PersonalTaskForm
            task={editingPersonalTask}
            onSave={handlePersonalTaskFormSubmit}
            onCancel={handlePersonalTaskFormCancel}
            isEditing={!!editingPersonalTask}
          />
        )}

        {/* Personal Task Detail View */}
        {selectedPersonalTask && !showPersonalTaskForm && (
          <PersonalTaskDetail
            task={selectedPersonalTask}
            onEdit={handleEditPersonalTask}
            onDelete={handlePersonalTaskDelete}
            onBack={handleBackToPersonalTasks}
            onTaskUpdate={handlePersonalTaskUpdate}
          />
        )}

        {/* Profile View */}
        {activeTab === "profile" && (
          <Card variant="modern" className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Profile Settings</CardTitle>
              <CardDescription>Manage your account information</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4 mb-6">
                <Avatar src={user?.avatar} alt={user?.name} size="xl" status="online" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{user?.name}</h3>
                  <p className="text-gray-600 dark:text-gray-400">{user?.email}</p>
                  <Badge variant="success" size="sm" className="mt-1">
                    {user?.role}
                  </Badge>
                </div>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Department
                    </label>
                    <p className="text-gray-900 dark:text-white">{user?.department?.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Employee ID
                    </label>
                    <p className="text-gray-900 dark:text-white">{user?.employeeId || "EMP001"}</p>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button variant="primary" className="mr-3">
                    Edit Profile
                  </Button>
                  <Button variant="outline">Change Password</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Calendar View */}
        {activeTab === "calendar" && (
          <Calendar
            onTaskSelect={(task) => {
              // Handle task selection from calendar
              if (task.type === "personal") {
                setSelectedPersonalTask(task)
                setActiveTab("personal-tasks")
              } else if (task.type === "project") {
                // Find the project and set it as selected
                const project = projects.find(p => p._id === task.project?._id)
                if (project) {
                  setSelectedProject(project)
                  setSelectedTask(task)
                  setActiveTab("project-tasks")
                }
              }
              // Admin tasks can be handled similarly if needed
            }}
            onCreateTask={() => {
              // Default to creating a personal task from calendar
              handleCreatePersonalTask()
            }}
          />
        )}
      </main>
    </div>
  )
}
