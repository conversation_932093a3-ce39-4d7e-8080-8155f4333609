import type { ContextOptions, Duration, Interval } from "./types.js";
/**
 * The {@link intervalToDuration} function options.
 */
export interface IntervalToDurationOptions extends ContextOptions<Date> {}
/**
 * @name intervalToDuration
 * @category Common Helpers
 * @summary Convert interval to duration
 *
 * @description
 * Convert an interval object to a duration object.
 *
 * @param interval - The interval to convert to duration
 * @param options - The context options
 *
 * @returns The duration object
 *
 * @example
 * // Get the duration between January 15, 1929 and April 4, 1968.
 * intervalToDuration({
 *   start: new Date(1929, 0, 15, 12, 0, 0),
 *   end: new Date(1968, 3, 4, 19, 5, 0)
 * });
 * //=> { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }
 */
export declare function intervalToDuration(
  interval: Interval,
  options?: IntervalToDurationOptions | undefined,
): Duration;
