// Test if calendar controller can be imported
try {
  console.log('Testing calendar controller import...')
  
  const { getCalendarTasks, getCalendarStats } = await import('./controllers/calendarController.js')
  
  console.log('✅ Calendar controller imported successfully')
  console.log('getCalendarTasks:', typeof getCalendarTasks)
  console.log('getCalendarStats:', typeof getCalendarStats)
  
} catch (error) {
  console.error('❌ Error importing calendar controller:', error.message)
  console.error('Stack:', error.stack)
}
