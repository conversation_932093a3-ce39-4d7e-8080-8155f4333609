import axios from 'axios'

const BASE_URL = 'http://localhost:5000/api'

// Test calendar API endpoints
async function testCalendarAPI() {
  try {
    console.log('🧪 Testing Calendar API Endpoints...\n')

    // First, we need to login as an employee to get a token
    console.log('1. Logging in as employee...')
    const loginResponse = await axios.post(`${BASE_URL}/auth/employee/login`, {
      username: 'ma<PERSON><PERSON>', // Using <PERSON><PERSON>'s username
      password: 'password123' // Default password
    })

    const token = loginResponse.data.token
    console.log('✅ Login successful')

    // Set up headers for authenticated requests
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }

    // Test calendar tasks endpoint
    console.log('\n2. Testing calendar tasks endpoint...')
    const startDate = new Date()
    startDate.setDate(1) // First day of current month
    const endDate = new Date()
    endDate.setMonth(endDate.getMonth() + 1, 0) // Last day of current month

    const calendarResponse = await axios.get(`${BASE_URL}/calendar/tasks`, {
      headers,
      params: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      }
    })

    console.log('✅ Calendar tasks endpoint working')
    console.log(`📊 Found ${calendarResponse.data.tasks.length} tasks`)
    
    if (calendarResponse.data.tasks.length > 0) {
      console.log('📝 Sample task:', JSON.stringify(calendarResponse.data.tasks[0], null, 2))
    }

    // Test calendar stats endpoint
    console.log('\n3. Testing calendar stats endpoint...')
    const statsResponse = await axios.get(`${BASE_URL}/calendar/stats`, {
      headers,
      params: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      }
    })

    console.log('✅ Calendar stats endpoint working')
    console.log('📈 Stats:', JSON.stringify(statsResponse.data.stats, null, 2))

    console.log('\n🎉 All calendar API tests passed!')

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message)
    
    if (error.response?.status === 401) {
      console.log('\n💡 Tip: Make sure you have a valid employee account with email "<EMAIL>" and password "password123"')
      console.log('Or update the credentials in this test script.')
    }
  }
}

// Run the test
testCalendarAPI()
