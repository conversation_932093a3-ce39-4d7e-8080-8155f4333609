import axios from 'axios'

const BASE_URL = 'http://localhost:5000'

async function testDirectRoute() {
  try {
    console.log('Testing direct route...')
    
    const response = await axios.get(`${BASE_URL}/api/test-direct`)
    console.log('✅ Direct route working!')
    console.log('Response:', response.data)
    
  } catch (error) {
    console.error('❌ Direct route failed:', error.response?.data || error.message)
  }
}

testDirectRoute()
