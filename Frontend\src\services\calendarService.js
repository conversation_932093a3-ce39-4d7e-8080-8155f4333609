import api from './api'

export const calendarService = {
  // Get tasks for calendar view within a date range
  getCalendarTasks: async (startDate, endDate) => {
    try {
      const response = await api.get('/calendar/tasks', {
        params: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
      })
      return response.data
    } catch (error) {
      console.error('Error fetching calendar tasks:', error)
      throw error
    }
  },

  // Get calendar statistics for a date range
  getCalendarStats: async (startDate, endDate) => {
    try {
      const response = await api.get('/calendar/stats', {
        params: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
      })
      return response.data
    } catch (error) {
      console.error('Error fetching calendar stats:', error)
      throw error
    }
  },
}
