import axios from 'axios'

const BASE_URL = 'http://localhost:5000'

async function testRootRoute() {
  try {
    console.log('Testing root route...')
    
    const response = await axios.get(`${BASE_URL}/`)
    console.log('✅ Root route working!')
    console.log('Response:', response.data)
    
  } catch (error) {
    console.error('❌ Root route failed:', error.response?.data || error.message)
  }
}

testRootRoute()
