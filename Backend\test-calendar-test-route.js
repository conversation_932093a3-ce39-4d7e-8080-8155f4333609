import axios from 'axios'

const BASE_URL = 'http://localhost:5000'

async function testCalendarTestRoute() {
  try {
    console.log('Testing calendar test route...')
    
    const response = await axios.get(`${BASE_URL}/api/calendar/test`)
    console.log('✅ Calendar test route working!')
    console.log('Response:', response.data)
    
  } catch (error) {
    console.error('❌ Calendar test route failed:', error.response?.data || error.message)
  }
}

testCalendarTestRoute()
